{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\ProfilePicture.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { MdVerified } from 'react-icons/md';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePicture = ({\n  user,\n  size = 'md',\n  showOnlineStatus = true,\n  className = '',\n  onClick = null,\n  style = {},\n  ...props\n}) => {\n  _s();\n  const [isOnline, setIsOnline] = useState(false);\n  const [imageError, setImageError] = useState(false);\n\n  // Conservative online status check - only show if explicitly online\n  useEffect(() => {\n    if (user && showOnlineStatus) {\n      // Only show online if explicitly marked as online in the user data\n      // This prevents false positives\n      setIsOnline(user.isOnline === true);\n    } else {\n      setIsOnline(false);\n    }\n  }, [user, showOnlineStatus]);\n  const getSizeConfig = () => {\n    switch (size) {\n      case 'xs':\n        return {\n          container: 'w-6 h-6',\n          text: 'text-xs font-semibold',\n          pixels: 24,\n          onlineSize: 8,\n          border: 'border-2'\n        };\n      case 'sm':\n        return {\n          container: 'w-8 h-8',\n          text: 'text-sm font-semibold',\n          pixels: 32,\n          onlineSize: 10,\n          border: 'border-2'\n        };\n      case 'md':\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base font-bold',\n          pixels: 48,\n          onlineSize: 12,\n          border: 'border-2'\n        };\n      case 'lg':\n        return {\n          container: 'w-16 h-16',\n          text: 'text-lg font-bold',\n          pixels: 64,\n          onlineSize: 16,\n          border: 'border-3'\n        };\n      case 'xl':\n        return {\n          container: 'w-20 h-20',\n          text: 'text-xl font-bold',\n          pixels: 80,\n          onlineSize: 18,\n          border: 'border-3'\n        };\n      case '2xl':\n        return {\n          container: 'w-24 h-24',\n          text: 'text-2xl font-bold',\n          pixels: 96,\n          onlineSize: 20,\n          border: 'border-4'\n        };\n      case '3xl':\n        return {\n          container: 'w-32 h-32',\n          text: 'text-3xl font-bold',\n          pixels: 128,\n          onlineSize: 24,\n          border: 'border-4'\n        };\n      default:\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base font-bold',\n          pixels: 48,\n          onlineSize: 12,\n          border: 'border-2'\n        };\n    }\n  };\n  const sizeConfig = getSizeConfig();\n  const isClickable = onClick !== null;\n\n  // Generate user initials\n  const getInitials = user => {\n    if (!user) return '?';\n    const name = user.name || user.username || 'User';\n    const words = name.trim().split(' ');\n    if (words.length >= 2) {\n      return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();\n    }\n    return name.charAt(0).toUpperCase();\n  };\n\n  // Generate consistent color based on user name\n  const getAvatarColor = user => {\n    if (!user) return '#6B7280'; // Gray for unknown user\n\n    const name = user.name || user.username || 'User';\n    const colors = ['#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16', '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9', '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF', '#EC4899', '#F43F5E'];\n    let hash = 0;\n    for (let i = 0; i < name.length; i++) {\n      hash = name.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    return colors[Math.abs(hash) % colors.length];\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative inline-block ${className}`,\n    style: {\n      padding: showOnlineStatus ? '2px' : '0'\n    },\n    ...props,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n          ${sizeConfig.container}\n          rounded-full overflow-hidden relative\n          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}\n        `,\n      style: {\n        background: '#f0f0f0',\n        border: showOnlineStatus && isOnline ? '4px solid #22c55e' : '2px solid #e5e7eb',\n        boxShadow: showOnlineStatus && isOnline ? '0 6px 16px rgba(34, 197, 94, 0.5), 0 2px 4px rgba(0, 0, 0, 0.1)' : '0 2px 8px rgba(0,0,0,0.15)',\n        transition: 'all 0.3s ease',\n        ...style\n      },\n      onClick: onClick,\n      children: [!user ?\n      /*#__PURE__*/\n      // Show fallback for undefined user\n      _jsxDEV(\"div\", {\n        className: `\n              rounded-full flex items-center justify-center w-full h-full\n              ${sizeConfig.text}\n            `,\n        style: {\n          background: getAvatarColor(user),\n          color: '#FFFFFF'\n        },\n        children: \"?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this) : (user !== null && user !== void 0 && user.profileImage || user !== null && user !== void 0 && user.profilePicture) && !imageError ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: user.profileImage || user.profilePicture,\n        alt: user.name || 'User',\n        className: \"object-cover rounded-full w-full h-full\",\n        style: {\n          objectFit: 'cover'\n        },\n        onError: e => {\n          // Fallback to initials if image fails to load\n          console.warn('Profile image failed to load:', e.target.src);\n          console.warn('User data:', user);\n          setImageError(true);\n        },\n        onLoad: () => {\n          console.log('Profile image loaded successfully:', user.profileImage || user.profilePicture);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this) : null, user && (!(user !== null && user !== void 0 && user.profileImage || user !== null && user !== void 0 && user.profilePicture) || imageError) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n              rounded-full flex items-center justify-center w-full h-full\n              ${sizeConfig.text}\n            `,\n        style: {\n          background: getAvatarColor(user),\n          color: '#FFFFFF'\n        },\n        children: getInitials(user)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), showOnlineStatus && (user === null || user === void 0 ? void 0 : user._id) && isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        width: `${sizeConfig.onlineSize}px`,\n        height: `${sizeConfig.onlineSize}px`,\n        bottom: '-2px',\n        right: '-2px',\n        zIndex: 999,\n        borderRadius: '50%',\n        backgroundColor: '#22c55e',\n        border: '3px solid #ffffff',\n        outline: 'none',\n        boxShadow: '0 4px 12px rgba(34, 197, 94, 0.8), 0 2px 4px rgba(0, 0, 0, 0.2)',\n        animation: 'pulse 2s infinite'\n      },\n      title: \"Online\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePicture, \"ebyCOBh0jPpVu9NC5VPflmDYVv4=\");\n_c = ProfilePicture;\nexport default ProfilePicture;\nvar _c;\n$RefreshReg$(_c, \"ProfilePicture\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "MdVerified", "jsxDEV", "_jsxDEV", "ProfilePicture", "user", "size", "showOnlineStatus", "className", "onClick", "style", "props", "_s", "isOnline", "setIsOnline", "imageError", "setImageError", "getSizeConfig", "container", "text", "pixels", "onlineSize", "border", "sizeConfig", "isClickable", "getInitials", "name", "username", "words", "trim", "split", "length", "char<PERSON>t", "toUpperCase", "getAvatarColor", "colors", "hash", "i", "charCodeAt", "Math", "abs", "padding", "children", "background", "boxShadow", "transition", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "profileImage", "profilePicture", "src", "alt", "objectFit", "onError", "e", "console", "warn", "target", "onLoad", "log", "_id", "position", "width", "height", "bottom", "right", "zIndex", "borderRadius", "backgroundColor", "outline", "animation", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/ProfilePicture.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { MdVerified } from 'react-icons/md';\n\nconst ProfilePicture = ({\n  user,\n  size = 'md',\n  showOnlineStatus = true,\n  className = '',\n  onClick = null,\n  style = {},\n  ...props\n}) => {\n\n  const [isOnline, setIsOnline] = useState(false);\n  const [imageError, setImageError] = useState(false);\n\n  // Conservative online status check - only show if explicitly online\n  useEffect(() => {\n    if (user && showOnlineStatus) {\n      // Only show online if explicitly marked as online in the user data\n      // This prevents false positives\n      setIsOnline(user.isOnline === true);\n    } else {\n      setIsOnline(false);\n    }\n  }, [user, showOnlineStatus]);\n\n  const getSizeConfig = () => {\n    switch (size) {\n      case 'xs':\n        return {\n          container: 'w-6 h-6',\n          text: 'text-xs font-semibold',\n          pixels: 24,\n          onlineSize: 8,\n          border: 'border-2'\n        };\n      case 'sm':\n        return {\n          container: 'w-8 h-8',\n          text: 'text-sm font-semibold',\n          pixels: 32,\n          onlineSize: 10,\n          border: 'border-2'\n        };\n      case 'md':\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base font-bold',\n          pixels: 48,\n          onlineSize: 12,\n          border: 'border-2'\n        };\n      case 'lg':\n        return {\n          container: 'w-16 h-16',\n          text: 'text-lg font-bold',\n          pixels: 64,\n          onlineSize: 16,\n          border: 'border-3'\n        };\n      case 'xl':\n        return {\n          container: 'w-20 h-20',\n          text: 'text-xl font-bold',\n          pixels: 80,\n          onlineSize: 18,\n          border: 'border-3'\n        };\n      case '2xl':\n        return {\n          container: 'w-24 h-24',\n          text: 'text-2xl font-bold',\n          pixels: 96,\n          onlineSize: 20,\n          border: 'border-4'\n        };\n      case '3xl':\n        return {\n          container: 'w-32 h-32',\n          text: 'text-3xl font-bold',\n          pixels: 128,\n          onlineSize: 24,\n          border: 'border-4'\n        };\n      default:\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base font-bold',\n          pixels: 48,\n          onlineSize: 12,\n          border: 'border-2'\n        };\n    }\n  };\n\n  const sizeConfig = getSizeConfig();\n  const isClickable = onClick !== null;\n\n  // Generate user initials\n  const getInitials = (user) => {\n    if (!user) return '?';\n    const name = user.name || user.username || 'User';\n    const words = name.trim().split(' ');\n    if (words.length >= 2) {\n      return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();\n    }\n    return name.charAt(0).toUpperCase();\n  };\n\n  // Generate consistent color based on user name\n  const getAvatarColor = (user) => {\n    if (!user) return '#6B7280'; // Gray for unknown user\n\n    const name = user.name || user.username || 'User';\n    const colors = [\n      '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',\n      '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',\n      '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',\n      '#EC4899', '#F43F5E'\n    ];\n\n    let hash = 0;\n    for (let i = 0; i < name.length; i++) {\n      hash = name.charCodeAt(i) + ((hash << 5) - hash);\n    }\n\n    return colors[Math.abs(hash) % colors.length];\n  };\n\n  return (\n    <div \n      className={`relative inline-block ${className}`} \n      style={{ padding: showOnlineStatus ? '2px' : '0' }}\n      {...props}\n    >\n      <div\n        className={`\n          ${sizeConfig.container}\n          rounded-full overflow-hidden relative\n          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}\n        `}\n        style={{\n          background: '#f0f0f0',\n          border: (showOnlineStatus && isOnline) ? '4px solid #22c55e' : '2px solid #e5e7eb',\n          boxShadow: (showOnlineStatus && isOnline)\n            ? '0 6px 16px rgba(34, 197, 94, 0.5), 0 2px 4px rgba(0, 0, 0, 0.1)'\n            : '0 2px 8px rgba(0,0,0,0.15)',\n          transition: 'all 0.3s ease',\n          ...style\n        }}\n        onClick={onClick}\n      >\n        {!user ? (\n          // Show fallback for undefined user\n          <div\n            className={`\n              rounded-full flex items-center justify-center w-full h-full\n              ${sizeConfig.text}\n            `}\n            style={{\n              background: getAvatarColor(user),\n              color: '#FFFFFF'\n            }}\n          >\n            ?\n          </div>\n        ) : (user?.profileImage || user?.profilePicture) && !imageError ? (\n          <img\n            src={user.profileImage || user.profilePicture}\n            alt={user.name || 'User'}\n            className=\"object-cover rounded-full w-full h-full\"\n            style={{ objectFit: 'cover' }}\n            onError={(e) => {\n              // Fallback to initials if image fails to load\n              console.warn('Profile image failed to load:', e.target.src);\n              console.warn('User data:', user);\n              setImageError(true);\n            }}\n            onLoad={() => {\n              console.log('Profile image loaded successfully:', user.profileImage || user.profilePicture);\n            }}\n          />\n        ) : null}\n        \n        {/* Fallback initials - show if user exists and (has no profile image OR image failed to load) */}\n        {user && (!(user?.profileImage || user?.profilePicture) || imageError) && (\n          <div\n            className={`\n              rounded-full flex items-center justify-center w-full h-full\n              ${sizeConfig.text}\n            `}\n            style={{\n              background: getAvatarColor(user),\n              color: '#FFFFFF'\n            }}\n          >\n            {getInitials(user)}\n          </div>\n        )}\n      </div>\n\n      {/* Online Status Indicator - Only show if actually online */}\n      {showOnlineStatus && user?._id && isOnline && (\n        <div\n          style={{\n            position: 'absolute',\n            width: `${sizeConfig.onlineSize}px`,\n            height: `${sizeConfig.onlineSize}px`,\n            bottom: '-2px',\n            right: '-2px',\n            zIndex: 999,\n            borderRadius: '50%',\n            backgroundColor: '#22c55e',\n            border: '3px solid #ffffff',\n            outline: 'none',\n            boxShadow: '0 4px 12px rgba(34, 197, 94, 0.8), 0 2px 4px rgba(0, 0, 0, 0.2)',\n            animation: 'pulse 2s infinite'\n          }}\n          title=\"Online\"\n        />\n      )}\n\n\n    </div>\n  );\n};\n\nexport default ProfilePicture;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,cAAc,GAAGA,CAAC;EACtBC,IAAI;EACJC,IAAI,GAAG,IAAI;EACXC,gBAAgB,GAAG,IAAI;EACvBC,SAAS,GAAG,EAAE;EACdC,OAAO,GAAG,IAAI;EACdC,KAAK,GAAG,CAAC,CAAC;EACV,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EAEJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIK,IAAI,IAAIE,gBAAgB,EAAE;MAC5B;MACA;MACAO,WAAW,CAACT,IAAI,CAACQ,QAAQ,KAAK,IAAI,CAAC;IACrC,CAAC,MAAM;MACLC,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC,EAAE,CAACT,IAAI,EAAEE,gBAAgB,CAAC,CAAC;EAE5B,MAAMU,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQX,IAAI;MACV,KAAK,IAAI;QACP,OAAO;UACLY,SAAS,EAAE,SAAS;UACpBC,IAAI,EAAE,uBAAuB;UAC7BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,CAAC;UACbC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,IAAI;QACP,OAAO;UACLJ,SAAS,EAAE,SAAS;UACpBC,IAAI,EAAE,uBAAuB;UAC7BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,IAAI;QACP,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,qBAAqB;UAC3BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,IAAI;QACP,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,mBAAmB;UACzBC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,IAAI;QACP,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,mBAAmB;UACzBC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,KAAK;QACR,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,oBAAoB;UAC1BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,KAAK;QACR,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,oBAAoB;UAC1BC,MAAM,EAAE,GAAG;UACXC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH;QACE,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,qBAAqB;UAC3BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;IACL;EACF,CAAC;EAED,MAAMC,UAAU,GAAGN,aAAa,CAAC,CAAC;EAClC,MAAMO,WAAW,GAAGf,OAAO,KAAK,IAAI;;EAEpC;EACA,MAAMgB,WAAW,GAAIpB,IAAI,IAAK;IAC5B,IAAI,CAACA,IAAI,EAAE,OAAO,GAAG;IACrB,MAAMqB,IAAI,GAAGrB,IAAI,CAACqB,IAAI,IAAIrB,IAAI,CAACsB,QAAQ,IAAI,MAAM;IACjD,MAAMC,KAAK,GAAGF,IAAI,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIF,KAAK,CAACG,MAAM,IAAI,CAAC,EAAE;MACrB,OAAO,CAACH,KAAK,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC,GAAGJ,KAAK,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,CAAC,CAAC;IAChE;IACA,OAAOP,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACrC,CAAC;;EAED;EACA,MAAMC,cAAc,GAAI7B,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,EAAE,OAAO,SAAS,CAAC,CAAC;;IAE7B,MAAMqB,IAAI,GAAGrB,IAAI,CAACqB,IAAI,IAAIrB,IAAI,CAACsB,QAAQ,IAAI,MAAM;IACjD,MAAMQ,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,CACrB;IAED,IAAIC,IAAI,GAAG,CAAC;IACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,IAAI,CAACK,MAAM,EAAEM,CAAC,EAAE,EAAE;MACpCD,IAAI,GAAGV,IAAI,CAACY,UAAU,CAACD,CAAC,CAAC,IAAI,CAACD,IAAI,IAAI,CAAC,IAAIA,IAAI,CAAC;IAClD;IAEA,OAAOD,MAAM,CAACI,IAAI,CAACC,GAAG,CAACJ,IAAI,CAAC,GAAGD,MAAM,CAACJ,MAAM,CAAC;EAC/C,CAAC;EAED,oBACE5B,OAAA;IACEK,SAAS,EAAG,yBAAwBA,SAAU,EAAE;IAChDE,KAAK,EAAE;MAAE+B,OAAO,EAAElC,gBAAgB,GAAG,KAAK,GAAG;IAAI,CAAE;IAAA,GAC/CI,KAAK;IAAA+B,QAAA,gBAETvC,OAAA;MACEK,SAAS,EAAG;AACpB,YAAYe,UAAU,CAACL,SAAU;AACjC;AACA,YAAYM,WAAW,GAAG,kEAAkE,GAAG,EAAG;AAClG,SAAU;MACFd,KAAK,EAAE;QACLiC,UAAU,EAAE,SAAS;QACrBrB,MAAM,EAAGf,gBAAgB,IAAIM,QAAQ,GAAI,mBAAmB,GAAG,mBAAmB;QAClF+B,SAAS,EAAGrC,gBAAgB,IAAIM,QAAQ,GACpC,iEAAiE,GACjE,4BAA4B;QAChCgC,UAAU,EAAE,eAAe;QAC3B,GAAGnC;MACL,CAAE;MACFD,OAAO,EAAEA,OAAQ;MAAAiC,QAAA,GAEhB,CAACrC,IAAI;MAAA;MACJ;MACAF,OAAA;QACEK,SAAS,EAAG;AACxB;AACA,gBAAgBe,UAAU,CAACJ,IAAK;AAChC,aAAc;QACFT,KAAK,EAAE;UACLiC,UAAU,EAAET,cAAc,CAAC7B,IAAI,CAAC;UAChCyC,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GACJ,CAAC7C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,YAAY,IAAI9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+C,cAAc,KAAK,CAACrC,UAAU,gBAC7DZ,OAAA;QACEkD,GAAG,EAAEhD,IAAI,CAAC8C,YAAY,IAAI9C,IAAI,CAAC+C,cAAe;QAC9CE,GAAG,EAAEjD,IAAI,CAACqB,IAAI,IAAI,MAAO;QACzBlB,SAAS,EAAC,yCAAyC;QACnDE,KAAK,EAAE;UAAE6C,SAAS,EAAE;QAAQ,CAAE;QAC9BC,OAAO,EAAGC,CAAC,IAAK;UACd;UACAC,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAEF,CAAC,CAACG,MAAM,CAACP,GAAG,CAAC;UAC3DK,OAAO,CAACC,IAAI,CAAC,YAAY,EAAEtD,IAAI,CAAC;UAChCW,aAAa,CAAC,IAAI,CAAC;QACrB,CAAE;QACF6C,MAAM,EAAEA,CAAA,KAAM;UACZH,OAAO,CAACI,GAAG,CAAC,oCAAoC,EAAEzD,IAAI,CAAC8C,YAAY,IAAI9C,IAAI,CAAC+C,cAAc,CAAC;QAC7F;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACA,IAAI,EAGP7C,IAAI,KAAK,EAAEA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,YAAY,IAAI9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+C,cAAc,CAAC,IAAIrC,UAAU,CAAC,iBACpEZ,OAAA;QACEK,SAAS,EAAG;AACxB;AACA,gBAAgBe,UAAU,CAACJ,IAAK;AAChC,aAAc;QACFT,KAAK,EAAE;UACLiC,UAAU,EAAET,cAAc,CAAC7B,IAAI,CAAC;UAChCyC,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EAEDjB,WAAW,CAACpB,IAAI;MAAC;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL3C,gBAAgB,KAAIF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,KAAIlD,QAAQ,iBACxCV,OAAA;MACEO,KAAK,EAAE;QACLsD,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAG,GAAE1C,UAAU,CAACF,UAAW,IAAG;QACnC6C,MAAM,EAAG,GAAE3C,UAAU,CAACF,UAAW,IAAG;QACpC8C,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,GAAG;QACXC,YAAY,EAAE,KAAK;QACnBC,eAAe,EAAE,SAAS;QAC1BjD,MAAM,EAAE,mBAAmB;QAC3BkD,OAAO,EAAE,MAAM;QACf5B,SAAS,EAAE,iEAAiE;QAC5E6B,SAAS,EAAE;MACb,CAAE;MACFC,KAAK,EAAC;IAAQ;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGE,CAAC;AAEV,CAAC;AAACtC,EAAA,CA/NIR,cAAc;AAAAuE,EAAA,GAAdvE,cAAc;AAiOpB,eAAeA,cAAc;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}