{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\NotificationBell.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { createPortal } from 'react-dom';\nimport { TbBell, TbBellRinging, TbCheck, TbX, TbSettings, TbTrash } from 'react-icons/tb';\nimport { getUserNotifications, getUnreadNotificationCount, markNotificationAsRead, markAllNotificationsAsRead } from '../../apicalls/notifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotificationBell = ({\n  className = ''\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [dropdownPosition, setDropdownPosition] = useState({\n    top: 0,\n    left: 0\n  });\n  const dropdownRef = useRef(null);\n  const buttonRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target) && buttonRef.current && !buttonRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Update dropdown position when opened\n  useEffect(() => {\n    if (isOpen && buttonRef.current) {\n      const rect = buttonRef.current.getBoundingClientRect();\n      const dropdownWidth = 384; // 24rem\n      const dropdownHeight = 500;\n\n      // Calculate position to ensure dropdown stays within viewport\n      let top = rect.bottom + 8;\n      let left = rect.left;\n\n      // Adjust if dropdown would go off right edge\n      if (left + dropdownWidth > window.innerWidth) {\n        left = window.innerWidth - dropdownWidth - 16;\n      }\n\n      // Adjust if dropdown would go off bottom edge\n      if (top + dropdownHeight > window.innerHeight) {\n        top = rect.top - dropdownHeight - 8;\n      }\n      setDropdownPosition({\n        top,\n        left\n      });\n    }\n  }, [isOpen]);\n\n  // Fetch unread count periodically\n  useEffect(() => {\n    const fetchUnreadCount = async () => {\n      try {\n        const response = await getUnreadNotificationCount();\n        if (response.success) {\n          setUnreadCount(response.data.unreadCount);\n        }\n      } catch (error) {\n        console.error('Error fetching unread count:', error);\n      }\n    };\n    fetchUnreadCount();\n    const interval = setInterval(fetchUnreadCount, 30000); // Every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Fetch notifications when dropdown opens\n  useEffect(() => {\n    if (isOpen && notifications.length === 0) {\n      fetchNotifications();\n    }\n  }, [isOpen]);\n  const fetchNotifications = async (pageNum = 1, reset = false) => {\n    if (loading) return;\n    setLoading(true);\n    try {\n      const response = await getUserNotifications({\n        page: pageNum,\n        limit: 10\n      });\n      if (response.success) {\n        const newNotifications = response.data.notifications;\n        if (reset || pageNum === 1) {\n          setNotifications(newNotifications);\n        } else {\n          setNotifications(prev => [...prev, ...newNotifications]);\n        }\n        setHasMore(newNotifications.length === 10);\n        setPage(pageNum);\n        setUnreadCount(response.data.unreadCount);\n      } else {\n        console.error('❌ Notifications fetch failed:', response.message);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching notifications:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleMarkAsRead = async notificationId => {\n    try {\n      const response = await markNotificationAsRead(notificationId);\n      if (response.success) {\n        setNotifications(prev => prev.map(notif => notif._id === notificationId ? {\n          ...notif,\n          isRead: true\n        } : notif));\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n  const handleMarkAllAsRead = async () => {\n    try {\n      const response = await markAllNotificationsAsRead();\n      if (response.success) {\n        setNotifications(prev => prev.map(notif => ({\n          ...notif,\n          isRead: true\n        })));\n        setUnreadCount(0);\n      }\n    } catch (error) {\n      console.error('Error marking all as read:', error);\n    }\n  };\n  const getNotificationIcon = type => {\n    switch (type) {\n      case 'new_exam':\n        return '🎯';\n      case 'new_study_material':\n        return '📚';\n      case 'forum_question_posted':\n        return '❓';\n      case 'forum_answer_received':\n        return '💡';\n      case 'level_up':\n        return '🎉';\n      case 'achievement_unlocked':\n        return '🏆';\n      default:\n        return '📢';\n    }\n  };\n  const formatTimeAgo = date => {\n    const now = new Date();\n    const notifDate = new Date(date);\n    const diffInMinutes = Math.floor((now - notifDate) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    return notifDate.toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative ${className}`,\n    ref: dropdownRef,\n    style: {\n      zIndex: 10000\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      ref: buttonRef,\n      onClick: e => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsOpen(!isOpen);\n      },\n      className: \"notification-bell-button relative p-2 text-gray-700 hover:text-blue-600 transition-colors\",\n      style: {\n        zIndex: 10001\n      },\n      children: [unreadCount > 0 ? /*#__PURE__*/_jsxDEV(TbBellRinging, {\n        className: \"w-5 h-5\",\n        style: {\n          color: '#ef4444'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TbBell, {\n        className: \"w-5 h-5\",\n        style: {\n          color: '#374151'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"notification-badge\",\n        style: {\n          position: 'absolute',\n          bottom: '-4px',\n          left: '50%',\n          transform: 'translateX(-50%)',\n          backgroundColor: '#ef4444',\n          color: '#ffffff',\n          fontSize: '10px',\n          fontWeight: '700',\n          minWidth: '20px',\n          width: '20px',\n          height: '20px',\n          borderRadius: '50%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          border: '2px solid #ffffff',\n          boxShadow: '0 2px 8px rgba(239, 68, 68, 0.6)',\n          zIndex: 1000\n        },\n        children: unreadCount > 99 ? '99+' : unreadCount\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/createPortal( /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: dropdownRef,\n      className: \"notification-dropdown\",\n      style: {\n        position: 'fixed',\n        top: dropdownPosition.top,\n        left: dropdownPosition.left,\n        zIndex: 99999,\n        width: '384px',\n        maxHeight: '500px',\n        backgroundColor: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',\n        border: '1px solid #e5e7eb',\n        overflow: 'hidden'\n      },\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderBottom: '1px solid #e5e7eb',\n          backgroundColor: '#f8fafc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TbBell, {\n            style: {\n              width: '20px',\n              height: '20px',\n              color: '#2563eb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontWeight: 'bold',\n              color: '#1f2937',\n              margin: 0\n            },\n            children: \"Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 17\n          }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#ef4444',\n              color: 'white',\n              fontSize: '12px',\n              padding: '4px 8px',\n              borderRadius: '9999px',\n              fontWeight: 'bold'\n            },\n            children: unreadCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleMarkAllAsRead,\n            className: \"text-sm text-blue-600 hover:text-blue-700 font-medium px-2 py-1 rounded hover:bg-blue-100 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-4 h-4 inline mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 21\n            }, this), \"Mark all read\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsOpen(false),\n            className: \"text-gray-400 hover:text-gray-600 p-1 rounded hover:bg-gray-100 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxHeight: '320px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            textAlign: 'center',\n            color: '#6b7280'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px'\n            },\n            children: \"\\uD83D\\uDD14 Notification Dropdown Test\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px'\n            },\n            children: [\"Loading: \", loading ? 'Yes' : 'No', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 52\n            }, this), \"Notifications: \", notifications.length, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 56\n            }, this), \"Unread: \", unreadCount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 15\n        }, this), loading && notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            textAlign: 'center',\n            color: '#6b7280'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '24px',\n              height: '24px',\n              border: '2px solid #2563eb',\n              borderTop: '2px solid transparent',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite',\n              margin: '0 auto 8px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 19\n          }, this), \"Loading notifications...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 17\n        }, this) : notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '32px',\n            textAlign: 'center',\n            color: '#6b7280'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TbBell, {\n            style: {\n              width: '48px',\n              height: '48px',\n              margin: '0 auto 8px',\n              opacity: 0.5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0\n            },\n            children: \"No notifications yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 17\n        }, this) : notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `notification-item group relative transition-all duration-200 ${!notification.isRead ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500' : 'hover:bg-gray-50'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 cursor-pointer\",\n            onClick: () => {\n              if (!notification.isRead) {\n                handleMarkAsRead(notification._id);\n              }\n              if (notification.actionUrl) {\n                window.location.href = notification.actionUrl;\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-10 h-10 rounded-full flex items-center justify-center text-lg flex-shrink-0 ${!notification.isRead ? 'bg-blue-100' : 'bg-gray-100'}`,\n                children: getNotificationIcon(notification.type)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `font-semibold text-sm line-clamp-1 ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`,\n                      children: notification.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 text-xs mt-1 line-clamp-2\",\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-400 text-xs\",\n                        children: formatTimeAgo(notification.createdAt)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 373,\n                        columnNumber: 33\n                      }, this), !notification.isRead && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-bold\",\n                        children: \"New\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              handleMarkAsRead(notification._id);\n              // Remove from local state\n              setNotifications(prev => prev.filter(n => n._id !== notification._id));\n            },\n            className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-gray-200 text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 21\n          }, this)]\n        }, notification._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 19\n        }, this)), hasMore && notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => fetchNotifications(page + 1),\n          disabled: loading,\n          className: \"w-full p-3 text-center text-blue-600 hover:bg-gray-50 text-sm font-medium disabled:opacity-50\",\n          children: loading ? 'Loading...' : 'Load more'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 11\n    }, this), document.body)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(NotificationBell, \"PWVAwyqLL1fujGgGnYUmT//86ds=\");\n_c = NotificationBell;\nexport default NotificationBell;\n\n// Add CSS animations to replace Framer Motion\nconst styles = `\n.notification-bell-button {\n  transition: transform 0.2s ease;\n}\n\n.notification-bell-button:hover {\n  transform: scale(1.05);\n}\n\n.notification-bell-button:active {\n  transform: scale(0.95);\n}\n\n.notification-badge {\n  animation: badgeAppear 0.3s ease-out;\n}\n\n.notification-dropdown {\n  animation: dropdownAppear 0.3s ease-out;\n}\n\n.notification-item {\n  animation: itemSlideIn 0.3s ease-out;\n}\n\n@keyframes badgeAppear {\n  from {\n    transform: scale(0);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n@keyframes dropdownAppear {\n  from {\n    opacity: 0;\n    transform: translateY(-10px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n@keyframes itemSlideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.textContent = styles;\n  document.head.appendChild(styleSheet);\n}\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "createPortal", "TbBell", "TbBellRinging", "TbCheck", "TbX", "TbSettings", "TbTrash", "getUserNotifications", "getUnreadNotificationCount", "markNotificationAsRead", "markAllNotificationsAsRead", "jsxDEV", "_jsxDEV", "NotificationBell", "className", "_s", "isOpen", "setIsOpen", "notifications", "setNotifications", "unreadCount", "setUnreadCount", "loading", "setLoading", "page", "setPage", "hasMore", "setHasMore", "dropdownPosition", "setDropdownPosition", "top", "left", "dropdownRef", "buttonRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "rect", "getBoundingClientRect", "dropdownWidth", "dropdownHeight", "bottom", "window", "innerWidth", "innerHeight", "fetchUnreadCount", "response", "success", "data", "error", "console", "interval", "setInterval", "clearInterval", "length", "fetchNotifications", "pageNum", "reset", "limit", "newNotifications", "prev", "message", "handleMarkAsRead", "notificationId", "map", "notif", "_id", "isRead", "Math", "max", "handleMarkAllAsRead", "getNotificationIcon", "type", "formatTimeAgo", "date", "now", "Date", "notifDate", "diffInMinutes", "floor", "diffInHours", "diffInDays", "toLocaleDateString", "ref", "style", "zIndex", "children", "onClick", "e", "preventDefault", "stopPropagation", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "transform", "backgroundColor", "fontSize", "fontWeight", "min<PERSON><PERSON><PERSON>", "width", "height", "borderRadius", "display", "alignItems", "justifyContent", "border", "boxShadow", "maxHeight", "overflow", "padding", "borderBottom", "gap", "margin", "overflowY", "textAlign", "marginBottom", "borderTop", "animation", "opacity", "notification", "actionUrl", "location", "href", "title", "createdAt", "filter", "n", "disabled", "body", "_c", "styles", "styleSheet", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/NotificationBell.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { createPortal } from 'react-dom';\nimport {\n  TbBell,\n  TbBellRinging,\n  TbCheck,\n  TbX,\n  TbSettings,\n  TbTrash\n} from 'react-icons/tb';\nimport { \n  getUserNotifications, \n  getUnreadNotificationCount,\n  markNotificationAsRead,\n  markAllNotificationsAsRead \n} from '../../apicalls/notifications';\n\nconst NotificationBell = ({ className = '' }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });\n  const dropdownRef = useRef(null);\n  const buttonRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target) &&\n          buttonRef.current && !buttonRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Update dropdown position when opened\n  useEffect(() => {\n    if (isOpen && buttonRef.current) {\n      const rect = buttonRef.current.getBoundingClientRect();\n\n      const dropdownWidth = 384; // 24rem\n      const dropdownHeight = 500;\n\n      // Calculate position to ensure dropdown stays within viewport\n      let top = rect.bottom + 8;\n      let left = rect.left;\n\n      // Adjust if dropdown would go off right edge\n      if (left + dropdownWidth > window.innerWidth) {\n        left = window.innerWidth - dropdownWidth - 16;\n      }\n\n      // Adjust if dropdown would go off bottom edge\n      if (top + dropdownHeight > window.innerHeight) {\n        top = rect.top - dropdownHeight - 8;\n      }\n\n      setDropdownPosition({ top, left });\n    }\n  }, [isOpen]);\n\n  // Fetch unread count periodically\n  useEffect(() => {\n    const fetchUnreadCount = async () => {\n      try {\n        const response = await getUnreadNotificationCount();\n        if (response.success) {\n          setUnreadCount(response.data.unreadCount);\n        }\n      } catch (error) {\n        console.error('Error fetching unread count:', error);\n      }\n    };\n\n    fetchUnreadCount();\n    const interval = setInterval(fetchUnreadCount, 30000); // Every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Fetch notifications when dropdown opens\n  useEffect(() => {\n    if (isOpen && notifications.length === 0) {\n      fetchNotifications();\n    }\n  }, [isOpen]);\n\n  const fetchNotifications = async (pageNum = 1, reset = false) => {\n    if (loading) return;\n\n    setLoading(true);\n    try {\n      const response = await getUserNotifications({\n        page: pageNum,\n        limit: 10\n      });\n\n      if (response.success) {\n        const newNotifications = response.data.notifications;\n\n        if (reset || pageNum === 1) {\n          setNotifications(newNotifications);\n        } else {\n          setNotifications(prev => [...prev, ...newNotifications]);\n        }\n\n        setHasMore(newNotifications.length === 10);\n        setPage(pageNum);\n        setUnreadCount(response.data.unreadCount);\n      } else {\n        console.error('❌ Notifications fetch failed:', response.message);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching notifications:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleMarkAsRead = async (notificationId) => {\n    try {\n      const response = await markNotificationAsRead(notificationId);\n      if (response.success) {\n        setNotifications(prev => \n          prev.map(notif => \n            notif._id === notificationId \n              ? { ...notif, isRead: true }\n              : notif\n          )\n        );\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n\n  const handleMarkAllAsRead = async () => {\n    try {\n      const response = await markAllNotificationsAsRead();\n      if (response.success) {\n        setNotifications(prev => \n          prev.map(notif => ({ ...notif, isRead: true }))\n        );\n        setUnreadCount(0);\n      }\n    } catch (error) {\n      console.error('Error marking all as read:', error);\n    }\n  };\n\n  const getNotificationIcon = (type) => {\n    switch (type) {\n      case 'new_exam':\n        return '🎯';\n      case 'new_study_material':\n        return '📚';\n      case 'forum_question_posted':\n        return '❓';\n      case 'forum_answer_received':\n        return '💡';\n      case 'level_up':\n        return '🎉';\n      case 'achievement_unlocked':\n        return '🏆';\n      default:\n        return '📢';\n    }\n  };\n\n  const formatTimeAgo = (date) => {\n    const now = new Date();\n    const notifDate = new Date(date);\n    const diffInMinutes = Math.floor((now - notifDate) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    \n    return notifDate.toLocaleDateString();\n  };\n\n  return (\n    <div className={`relative ${className}`} ref={dropdownRef} style={{ zIndex: 10000 }}>\n      {/* Bell Icon */}\n      <button\n        ref={buttonRef}\n        onClick={(e) => {\n          e.preventDefault();\n          e.stopPropagation();\n          setIsOpen(!isOpen);\n        }}\n        className=\"notification-bell-button relative p-2 text-gray-700 hover:text-blue-600 transition-colors\"\n        style={{ zIndex: 10001 }}\n      >\n        {unreadCount > 0 ? (\n          <TbBellRinging\n            className=\"w-5 h-5\"\n            style={{ color: '#ef4444' }}\n          />\n        ) : (\n          <TbBell\n            className=\"w-5 h-5\"\n            style={{ color: '#374151' }}\n          />\n        )}\n\n        {/* Unread count badge - positioned below the bell */}\n        {unreadCount > 0 && (\n          <span\n            className=\"notification-badge\"\n            style={{\n              position: 'absolute',\n              bottom: '-4px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              backgroundColor: '#ef4444',\n              color: '#ffffff',\n              fontSize: '10px',\n              fontWeight: '700',\n              minWidth: '20px',\n              width: '20px',\n              height: '20px',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              border: '2px solid #ffffff',\n              boxShadow: '0 2px 8px rgba(239, 68, 68, 0.6)',\n              zIndex: 1000\n            }}\n          >\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* Dropdown Portal */}\n      {isOpen && createPortal(\n          <div\n            ref={dropdownRef}\n            className=\"notification-dropdown\"\n              style={{\n                position: 'fixed',\n                top: dropdownPosition.top,\n                left: dropdownPosition.left,\n                zIndex: 99999,\n                width: '384px',\n                maxHeight: '500px',\n                backgroundColor: 'white',\n                borderRadius: '16px',\n                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',\n                border: '1px solid #e5e7eb',\n                overflow: 'hidden'\n              }}\n              onClick={(e) => e.stopPropagation()}\n            >\n            {/* Test Header */}\n            <div style={{ padding: '16px', borderBottom: '1px solid #e5e7eb', backgroundColor: '#f8fafc' }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                <TbBell style={{ width: '20px', height: '20px', color: '#2563eb' }} />\n                <h3 style={{ fontWeight: 'bold', color: '#1f2937', margin: 0 }}>Notifications</h3>\n                {unreadCount > 0 && (\n                  <span style={{\n                    backgroundColor: '#ef4444',\n                    color: 'white',\n                    fontSize: '12px',\n                    padding: '4px 8px',\n                    borderRadius: '9999px',\n                    fontWeight: 'bold'\n                  }}>\n                    {unreadCount}\n                  </span>\n                )}\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                {unreadCount > 0 && (\n                  <button\n                    onClick={handleMarkAllAsRead}\n                    className=\"text-sm text-blue-600 hover:text-blue-700 font-medium px-2 py-1 rounded hover:bg-blue-100 transition-colors\"\n                  >\n                    <TbCheck className=\"w-4 h-4 inline mr-1\" />\n                    Mark all read\n                  </button>\n                )}\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"text-gray-400 hover:text-gray-600 p-1 rounded hover:bg-gray-100 transition-colors\"\n                >\n                  <TbX className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Test Content */}\n            <div style={{ maxHeight: '320px', overflowY: 'auto' }}>\n              <div style={{ padding: '16px', textAlign: 'center', color: '#6b7280' }}>\n                <div style={{ marginBottom: '8px' }}>🔔 Notification Dropdown Test</div>\n                <div style={{ fontSize: '14px' }}>\n                  Loading: {loading ? 'Yes' : 'No'}<br/>\n                  Notifications: {notifications.length}<br/>\n                  Unread: {unreadCount}\n                </div>\n              </div>\n              {loading && notifications.length === 0 ? (\n                <div style={{ padding: '16px', textAlign: 'center', color: '#6b7280' }}>\n                  <div style={{\n                    width: '24px',\n                    height: '24px',\n                    border: '2px solid #2563eb',\n                    borderTop: '2px solid transparent',\n                    borderRadius: '50%',\n                    animation: 'spin 1s linear infinite',\n                    margin: '0 auto 8px'\n                  }}></div>\n                  Loading notifications...\n                </div>\n              ) : notifications.length === 0 ? (\n                <div style={{ padding: '32px', textAlign: 'center', color: '#6b7280' }}>\n                  <TbBell style={{ width: '48px', height: '48px', margin: '0 auto 8px', opacity: 0.5 }} />\n                  <p style={{ margin: 0 }}>No notifications yet</p>\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <div\n                    key={notification._id}\n                    className={`notification-item group relative transition-all duration-200 ${\n                      !notification.isRead\n                        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500'\n                        : 'hover:bg-gray-50'\n                    }`}\n                  >\n                    <div\n                      className=\"p-4 cursor-pointer\"\n                      onClick={() => {\n                        if (!notification.isRead) {\n                          handleMarkAsRead(notification._id);\n                        }\n                        if (notification.actionUrl) {\n                          window.location.href = notification.actionUrl;\n                        }\n                      }}\n                    >\n                      <div className=\"flex items-start gap-3\">\n                        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg flex-shrink-0 ${\n                          !notification.isRead ? 'bg-blue-100' : 'bg-gray-100'\n                        }`}>\n                          {getNotificationIcon(notification.type)}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-start justify-between\">\n                            <div className=\"flex-1\">\n                              <p className={`font-semibold text-sm line-clamp-1 ${\n                                !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                              }`}>\n                                {notification.title}\n                              </p>\n                              <p className=\"text-gray-600 text-xs mt-1 line-clamp-2\">\n                                {notification.message}\n                              </p>\n                              <div className=\"flex items-center justify-between mt-2\">\n                                <p className=\"text-gray-400 text-xs\">\n                                  {formatTimeAgo(notification.createdAt)}\n                                </p>\n                                {!notification.isRead && (\n                                  <span className=\"bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-bold\">\n                                    New\n                                  </span>\n                                )}\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Individual close button */}\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleMarkAsRead(notification._id);\n                        // Remove from local state\n                        setNotifications(prev => prev.filter(n => n._id !== notification._id));\n                      }}\n                      className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-gray-200 text-gray-400 hover:text-gray-600\"\n                    >\n                      <TbX className=\"w-3 h-3\" />\n                    </button>\n                  </div>\n                ))\n              )}\n              \n              {/* Load More */}\n              {hasMore && notifications.length > 0 && (\n                <button\n                  onClick={() => fetchNotifications(page + 1)}\n                  disabled={loading}\n                  className=\"w-full p-3 text-center text-blue-600 hover:bg-gray-50 text-sm font-medium disabled:opacity-50\"\n                >\n                  {loading ? 'Loading...' : 'Load more'}\n                </button>\n              )}\n            </div>\n          </div>,\n        document.body\n      )}\n    </div>\n  );\n};\n\nexport default NotificationBell;\n\n// Add CSS animations to replace Framer Motion\nconst styles = `\n.notification-bell-button {\n  transition: transform 0.2s ease;\n}\n\n.notification-bell-button:hover {\n  transform: scale(1.05);\n}\n\n.notification-bell-button:active {\n  transform: scale(0.95);\n}\n\n.notification-badge {\n  animation: badgeAppear 0.3s ease-out;\n}\n\n.notification-dropdown {\n  animation: dropdownAppear 0.3s ease-out;\n}\n\n.notification-item {\n  animation: itemSlideIn 0.3s ease-out;\n}\n\n@keyframes badgeAppear {\n  from {\n    transform: scale(0);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n@keyframes dropdownAppear {\n  from {\n    opacity: 0;\n    transform: translateY(-10px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n@keyframes itemSlideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.textContent = styles;\n  document.head.appendChild(styleSheet);\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,QAAQ,WAAW;AACxC,SACEC,MAAM,EACNC,aAAa,EACbC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,OAAO,QACF,gBAAgB;AACvB,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,0BAA0B,QACrB,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC;IAAEiC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAMC,WAAW,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkC,SAAS,GAAGlC,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACAD,SAAS,CAAC,MAAM;IACd,MAAMoC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,IAClEL,SAAS,CAACG,OAAO,IAAI,CAACH,SAAS,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAClErB,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAEDsB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApC,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,IAAIiB,SAAS,CAACG,OAAO,EAAE;MAC/B,MAAMM,IAAI,GAAGT,SAAS,CAACG,OAAO,CAACO,qBAAqB,CAAC,CAAC;MAEtD,MAAMC,aAAa,GAAG,GAAG,CAAC,CAAC;MAC3B,MAAMC,cAAc,GAAG,GAAG;;MAE1B;MACA,IAAIf,GAAG,GAAGY,IAAI,CAACI,MAAM,GAAG,CAAC;MACzB,IAAIf,IAAI,GAAGW,IAAI,CAACX,IAAI;;MAEpB;MACA,IAAIA,IAAI,GAAGa,aAAa,GAAGG,MAAM,CAACC,UAAU,EAAE;QAC5CjB,IAAI,GAAGgB,MAAM,CAACC,UAAU,GAAGJ,aAAa,GAAG,EAAE;MAC/C;;MAEA;MACA,IAAId,GAAG,GAAGe,cAAc,GAAGE,MAAM,CAACE,WAAW,EAAE;QAC7CnB,GAAG,GAAGY,IAAI,CAACZ,GAAG,GAAGe,cAAc,GAAG,CAAC;MACrC;MAEAhB,mBAAmB,CAAC;QAAEC,GAAG;QAAEC;MAAK,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;;EAEZ;EACAlB,SAAS,CAAC,MAAM;IACd,MAAMoD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM3C,0BAA0B,CAAC,CAAC;QACnD,IAAI2C,QAAQ,CAACC,OAAO,EAAE;UACpB/B,cAAc,CAAC8B,QAAQ,CAACE,IAAI,CAACjC,WAAW,CAAC;QAC3C;MACF,CAAC,CAAC,OAAOkC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDJ,gBAAgB,CAAC,CAAC;IAClB,MAAMM,QAAQ,GAAGC,WAAW,CAACP,gBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEvD,OAAO,MAAMQ,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1D,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,IAAIE,aAAa,CAACyC,MAAM,KAAK,CAAC,EAAE;MACxCC,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAAC5C,MAAM,CAAC,CAAC;EAEZ,MAAM4C,kBAAkB,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,EAAEC,KAAK,GAAG,KAAK,KAAK;IAC/D,IAAIxC,OAAO,EAAE;IAEbC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM4B,QAAQ,GAAG,MAAM5C,oBAAoB,CAAC;QAC1CiB,IAAI,EAAEqC,OAAO;QACbE,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,IAAIZ,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMY,gBAAgB,GAAGb,QAAQ,CAACE,IAAI,CAACnC,aAAa;QAEpD,IAAI4C,KAAK,IAAID,OAAO,KAAK,CAAC,EAAE;UAC1B1C,gBAAgB,CAAC6C,gBAAgB,CAAC;QACpC,CAAC,MAAM;UACL7C,gBAAgB,CAAC8C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,gBAAgB,CAAC,CAAC;QAC1D;QAEArC,UAAU,CAACqC,gBAAgB,CAACL,MAAM,KAAK,EAAE,CAAC;QAC1ClC,OAAO,CAACoC,OAAO,CAAC;QAChBxC,cAAc,CAAC8B,QAAQ,CAACE,IAAI,CAACjC,WAAW,CAAC;MAC3C,CAAC,MAAM;QACLmC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEH,QAAQ,CAACe,OAAO,CAAC;MAClE;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4C,gBAAgB,GAAG,MAAOC,cAAc,IAAK;IACjD,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAM1C,sBAAsB,CAAC2D,cAAc,CAAC;MAC7D,IAAIjB,QAAQ,CAACC,OAAO,EAAE;QACpBjC,gBAAgB,CAAC8C,IAAI,IACnBA,IAAI,CAACI,GAAG,CAACC,KAAK,IACZA,KAAK,CAACC,GAAG,KAAKH,cAAc,GACxB;UAAE,GAAGE,KAAK;UAAEE,MAAM,EAAE;QAAK,CAAC,GAC1BF,KACN,CACF,CAAC;QACDjD,cAAc,CAAC4C,IAAI,IAAIQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAET,IAAI,GAAG,CAAC,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF,CAAC;EAED,MAAMqB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAMzC,0BAA0B,CAAC,CAAC;MACnD,IAAIyC,QAAQ,CAACC,OAAO,EAAE;QACpBjC,gBAAgB,CAAC8C,IAAI,IACnBA,IAAI,CAACI,GAAG,CAACC,KAAK,KAAK;UAAE,GAAGA,KAAK;UAAEE,MAAM,EAAE;QAAK,CAAC,CAAC,CAChD,CAAC;QACDnD,cAAc,CAAC,CAAC,CAAC;MACnB;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMsB,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,oBAAoB;QACvB,OAAO,IAAI;MACb,KAAK,uBAAuB;QAC1B,OAAO,GAAG;MACZ,KAAK,uBAAuB;QAC1B,OAAO,IAAI;MACb,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,sBAAsB;QACzB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMC,aAAa,GAAIC,IAAI,IAAK;IAC9B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IAChC,MAAMI,aAAa,GAAGV,IAAI,CAACW,KAAK,CAAC,CAACJ,GAAG,GAAGE,SAAS,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjE,IAAIC,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;IACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAQ,GAAEA,aAAc,OAAM;IAEtD,MAAME,WAAW,GAAGZ,IAAI,CAACW,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIE,WAAW,GAAG,EAAE,EAAE,OAAQ,GAAEA,WAAY,OAAM;IAElD,MAAMC,UAAU,GAAGb,IAAI,CAACW,KAAK,CAACC,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAQ,GAAEA,UAAW,OAAM;IAE/C,OAAOJ,SAAS,CAACK,kBAAkB,CAAC,CAAC;EACvC,CAAC;EAED,oBACE3E,OAAA;IAAKE,SAAS,EAAG,YAAWA,SAAU,EAAE;IAAC0E,GAAG,EAAExD,WAAY;IAACyD,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAM,CAAE;IAAAC,QAAA,gBAElF/E,OAAA;MACE4E,GAAG,EAAEvD,SAAU;MACf2D,OAAO,EAAGC,CAAC,IAAK;QACdA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;QACnB9E,SAAS,CAAC,CAACD,MAAM,CAAC;MACpB,CAAE;MACFF,SAAS,EAAC,2FAA2F;MACrG2E,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAM,CAAE;MAAAC,QAAA,GAExBvE,WAAW,GAAG,CAAC,gBACdR,OAAA,CAACV,aAAa;QACZY,SAAS,EAAC,SAAS;QACnB2E,KAAK,EAAE;UAAEO,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,gBAEFxF,OAAA,CAACX,MAAM;QACLa,SAAS,EAAC,SAAS;QACnB2E,KAAK,EAAE;UAAEO,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF,EAGAhF,WAAW,GAAG,CAAC,iBACdR,OAAA;QACEE,SAAS,EAAC,oBAAoB;QAC9B2E,KAAK,EAAE;UACLY,QAAQ,EAAE,UAAU;UACpBvD,MAAM,EAAE,MAAM;UACdf,IAAI,EAAE,KAAK;UACXuE,SAAS,EAAE,kBAAkB;UAC7BC,eAAe,EAAE,SAAS;UAC1BP,KAAK,EAAE,SAAS;UAChBQ,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,kCAAkC;UAC7CxB,MAAM,EAAE;QACV,CAAE;QAAAC,QAAA,EAEDvE,WAAW,GAAG,EAAE,GAAG,KAAK,GAAGA;MAAW;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,EAGRpF,MAAM,iBAAIhB,YAAY,eACnBY,OAAA;MACE4E,GAAG,EAAExD,WAAY;MACjBlB,SAAS,EAAC,uBAAuB;MAC/B2E,KAAK,EAAE;QACLY,QAAQ,EAAE,OAAO;QACjBvE,GAAG,EAAEF,gBAAgB,CAACE,GAAG;QACzBC,IAAI,EAAEH,gBAAgB,CAACG,IAAI;QAC3B2D,MAAM,EAAE,KAAK;QACbiB,KAAK,EAAE,OAAO;QACdQ,SAAS,EAAE,OAAO;QAClBZ,eAAe,EAAE,OAAO;QACxBM,YAAY,EAAE,MAAM;QACpBK,SAAS,EAAE,sEAAsE;QACjFD,MAAM,EAAE,mBAAmB;QAC3BG,QAAQ,EAAE;MACZ,CAAE;MACFxB,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACE,eAAe,CAAC,CAAE;MAAAJ,QAAA,gBAGtC/E,OAAA;QAAK6E,KAAK,EAAE;UAAE4B,OAAO,EAAE,MAAM;UAAEC,YAAY,EAAE,mBAAmB;UAAEf,eAAe,EAAE;QAAU,CAAE;QAAAZ,QAAA,gBAC7F/E,OAAA;UAAK6E,KAAK,EAAE;YAAEqB,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEQ,GAAG,EAAE;UAAM,CAAE;UAAA5B,QAAA,gBAChE/E,OAAA,CAACX,MAAM;YAACwF,KAAK,EAAE;cAAEkB,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE,MAAM;cAAEZ,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtExF,OAAA;YAAI6E,KAAK,EAAE;cAAEgB,UAAU,EAAE,MAAM;cAAET,KAAK,EAAE,SAAS;cAAEwB,MAAM,EAAE;YAAE,CAAE;YAAA7B,QAAA,EAAC;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjFhF,WAAW,GAAG,CAAC,iBACdR,OAAA;YAAM6E,KAAK,EAAE;cACXc,eAAe,EAAE,SAAS;cAC1BP,KAAK,EAAE,OAAO;cACdQ,QAAQ,EAAE,MAAM;cAChBa,OAAO,EAAE,SAAS;cAClBR,YAAY,EAAE,QAAQ;cACtBJ,UAAU,EAAE;YACd,CAAE;YAAAd,QAAA,EACCvE;UAAW;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNxF,OAAA;UAAKE,SAAS,EAAC,6BAA6B;UAAA6E,QAAA,GACzCvE,WAAW,GAAG,CAAC,iBACdR,OAAA;YACEgF,OAAO,EAAEjB,mBAAoB;YAC7B7D,SAAS,EAAC,6GAA6G;YAAA6E,QAAA,gBAEvH/E,OAAA,CAACT,OAAO;cAACW,SAAS,EAAC;YAAqB;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACDxF,OAAA;YACEgF,OAAO,EAAEA,CAAA,KAAM3E,SAAS,CAAC,KAAK,CAAE;YAChCH,SAAS,EAAC,mFAAmF;YAAA6E,QAAA,eAE7F/E,OAAA,CAACR,GAAG;cAACU,SAAS,EAAC;YAAS;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxF,OAAA;QAAK6E,KAAK,EAAE;UAAE0B,SAAS,EAAE,OAAO;UAAEM,SAAS,EAAE;QAAO,CAAE;QAAA9B,QAAA,gBACpD/E,OAAA;UAAK6E,KAAK,EAAE;YAAE4B,OAAO,EAAE,MAAM;YAAEK,SAAS,EAAE,QAAQ;YAAE1B,KAAK,EAAE;UAAU,CAAE;UAAAL,QAAA,gBACrE/E,OAAA;YAAK6E,KAAK,EAAE;cAAEkC,YAAY,EAAE;YAAM,CAAE;YAAAhC,QAAA,EAAC;UAA6B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxExF,OAAA;YAAK6E,KAAK,EAAE;cAAEe,QAAQ,EAAE;YAAO,CAAE;YAAAb,QAAA,GAAC,WACvB,EAACrE,OAAO,GAAG,KAAK,GAAG,IAAI,eAACV,OAAA;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mBACvB,EAAClF,aAAa,CAACyC,MAAM,eAAC/C,OAAA;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,YAClC,EAAChF,WAAW;UAAA;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACL9E,OAAO,IAAIJ,aAAa,CAACyC,MAAM,KAAK,CAAC,gBACpC/C,OAAA;UAAK6E,KAAK,EAAE;YAAE4B,OAAO,EAAE,MAAM;YAAEK,SAAS,EAAE,QAAQ;YAAE1B,KAAK,EAAE;UAAU,CAAE;UAAAL,QAAA,gBACrE/E,OAAA;YAAK6E,KAAK,EAAE;cACVkB,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdK,MAAM,EAAE,mBAAmB;cAC3BW,SAAS,EAAE,uBAAuB;cAClCf,YAAY,EAAE,KAAK;cACnBgB,SAAS,EAAE,yBAAyB;cACpCL,MAAM,EAAE;YACV;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4BAEX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GACJlF,aAAa,CAACyC,MAAM,KAAK,CAAC,gBAC5B/C,OAAA;UAAK6E,KAAK,EAAE;YAAE4B,OAAO,EAAE,MAAM;YAAEK,SAAS,EAAE,QAAQ;YAAE1B,KAAK,EAAE;UAAU,CAAE;UAAAL,QAAA,gBACrE/E,OAAA,CAACX,MAAM;YAACwF,KAAK,EAAE;cAAEkB,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE,MAAM;cAAEY,MAAM,EAAE,YAAY;cAAEM,OAAO,EAAE;YAAI;UAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxFxF,OAAA;YAAG6E,KAAK,EAAE;cAAE+B,MAAM,EAAE;YAAE,CAAE;YAAA7B,QAAA,EAAC;UAAoB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,GAENlF,aAAa,CAACmD,GAAG,CAAE0D,YAAY,iBAC7BnH,OAAA;UAEEE,SAAS,EAAG,gEACV,CAACiH,YAAY,CAACvD,MAAM,GAChB,yEAAyE,GACzE,kBACL,EAAE;UAAAmB,QAAA,gBAEH/E,OAAA;YACEE,SAAS,EAAC,oBAAoB;YAC9B8E,OAAO,EAAEA,CAAA,KAAM;cACb,IAAI,CAACmC,YAAY,CAACvD,MAAM,EAAE;gBACxBL,gBAAgB,CAAC4D,YAAY,CAACxD,GAAG,CAAC;cACpC;cACA,IAAIwD,YAAY,CAACC,SAAS,EAAE;gBAC1BjF,MAAM,CAACkF,QAAQ,CAACC,IAAI,GAAGH,YAAY,CAACC,SAAS;cAC/C;YACF,CAAE;YAAArC,QAAA,eAEF/E,OAAA;cAAKE,SAAS,EAAC,wBAAwB;cAAA6E,QAAA,gBACrC/E,OAAA;gBAAKE,SAAS,EAAG,iFACf,CAACiH,YAAY,CAACvD,MAAM,GAAG,aAAa,GAAG,aACxC,EAAE;gBAAAmB,QAAA,EACAf,mBAAmB,CAACmD,YAAY,CAAClD,IAAI;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACNxF,OAAA;gBAAKE,SAAS,EAAC,gBAAgB;gBAAA6E,QAAA,eAC7B/E,OAAA;kBAAKE,SAAS,EAAC,kCAAkC;kBAAA6E,QAAA,eAC/C/E,OAAA;oBAAKE,SAAS,EAAC,QAAQ;oBAAA6E,QAAA,gBACrB/E,OAAA;sBAAGE,SAAS,EAAG,sCACb,CAACiH,YAAY,CAACvD,MAAM,GAAG,eAAe,GAAG,eAC1C,EAAE;sBAAAmB,QAAA,EACAoC,YAAY,CAACI;oBAAK;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACJxF,OAAA;sBAAGE,SAAS,EAAC,yCAAyC;sBAAA6E,QAAA,EACnDoC,YAAY,CAAC7D;oBAAO;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACJxF,OAAA;sBAAKE,SAAS,EAAC,wCAAwC;sBAAA6E,QAAA,gBACrD/E,OAAA;wBAAGE,SAAS,EAAC,uBAAuB;wBAAA6E,QAAA,EACjCb,aAAa,CAACiD,YAAY,CAACK,SAAS;sBAAC;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC,EACH,CAAC2B,YAAY,CAACvD,MAAM,iBACnB5D,OAAA;wBAAME,SAAS,EAAC,iEAAiE;wBAAA6E,QAAA,EAAC;sBAElF;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxF,OAAA;YACEgF,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACE,eAAe,CAAC,CAAC;cACnB5B,gBAAgB,CAAC4D,YAAY,CAACxD,GAAG,CAAC;cAClC;cACApD,gBAAgB,CAAC8C,IAAI,IAAIA,IAAI,CAACoE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/D,GAAG,KAAKwD,YAAY,CAACxD,GAAG,CAAC,CAAC;YACxE,CAAE;YACFzD,SAAS,EAAC,6IAA6I;YAAA6E,QAAA,eAEvJ/E,OAAA,CAACR,GAAG;cAACU,SAAS,EAAC;YAAS;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA,GA9DJ2B,YAAY,CAACxD,GAAG;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+DlB,CACN,CACF,EAGA1E,OAAO,IAAIR,aAAa,CAACyC,MAAM,GAAG,CAAC,iBAClC/C,OAAA;UACEgF,OAAO,EAAEA,CAAA,KAAMhC,kBAAkB,CAACpC,IAAI,GAAG,CAAC,CAAE;UAC5C+G,QAAQ,EAAEjH,OAAQ;UAClBR,SAAS,EAAC,+FAA+F;UAAA6E,QAAA,EAExGrE,OAAO,GAAG,YAAY,GAAG;QAAW;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACR7D,QAAQ,CAACiG,IACX,CAAC;EAAA;IAAAvC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrF,EAAA,CAlZIF,gBAAgB;AAAA4H,EAAA,GAAhB5H,gBAAgB;AAoZtB,eAAeA,gBAAgB;;AAE/B;AACA,MAAM6H,MAAM,GAAI;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOnG,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMoG,UAAU,GAAGpG,QAAQ,CAACqG,aAAa,CAAC,OAAO,CAAC;EAClDD,UAAU,CAACE,WAAW,GAAGH,MAAM;EAC/BnG,QAAQ,CAACuG,IAAI,CAACC,WAAW,CAACJ,UAAU,CAAC;AACvC;AAAC,IAAAF,EAAA;AAAAO,YAAA,CAAAP,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}