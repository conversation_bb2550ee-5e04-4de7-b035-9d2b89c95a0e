import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import {
  TbBell,
  TbBellRinging,
  TbCheck,
  TbX,
  TbSettings,
  TbTrash
} from 'react-icons/tb';
import { 
  getUserNotifications, 
  getUnreadNotificationCount,
  markNotificationAsRead,
  markAllNotificationsAsRead 
} from '../../apicalls/notifications';

const NotificationBell = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 });
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target) &&
          buttonRef.current && !buttonRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Update dropdown position when opened
  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + 8,
        right: window.innerWidth - rect.right
      });
    }
  }, [isOpen]);

  // Fetch unread count periodically
  useEffect(() => {
    const fetchUnreadCount = async () => {
      try {
        const response = await getUnreadNotificationCount();
        if (response.success) {
          setUnreadCount(response.data.unreadCount);
        }
      } catch (error) {
        console.error('Error fetching unread count:', error);
      }
    };

    fetchUnreadCount();
    const interval = setInterval(fetchUnreadCount, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Fetch notifications when dropdown opens
  useEffect(() => {
    if (isOpen && notifications.length === 0) {
      fetchNotifications();
    }
  }, [isOpen]);

  const fetchNotifications = async (pageNum = 1, reset = false) => {
    if (loading) return;

    console.log('📡 Fetching notifications, page:', pageNum);
    setLoading(true);
    try {
      const response = await getUserNotifications({
        page: pageNum,
        limit: 10
      });

      console.log('📡 Notifications response:', response);

      if (response.success) {
        const newNotifications = response.data.notifications;
        console.log('📡 New notifications:', newNotifications);

        if (reset || pageNum === 1) {
          setNotifications(newNotifications);
        } else {
          setNotifications(prev => [...prev, ...newNotifications]);
        }

        setHasMore(newNotifications.length === 10);
        setPage(pageNum);
        setUnreadCount(response.data.unreadCount);
      } else {
        console.error('❌ Notifications fetch failed:', response.message);
      }
    } catch (error) {
      console.error('❌ Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (notificationId) => {
    try {
      const response = await markNotificationAsRead(notificationId);
      if (response.success) {
        setNotifications(prev => 
          prev.map(notif => 
            notif._id === notificationId 
              ? { ...notif, isRead: true }
              : notif
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllNotificationsAsRead();
      if (response.success) {
        setNotifications(prev => 
          prev.map(notif => ({ ...notif, isRead: true }))
        );
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('Error marking all as read:', error);
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'new_exam':
        return '🎯';
      case 'new_study_material':
        return '📚';
      case 'forum_question_posted':
        return '❓';
      case 'forum_answer_received':
        return '💡';
      case 'level_up':
        return '🎉';
      case 'achievement_unlocked':
        return '🏆';
      default:
        return '📢';
    }
  };

  const formatTimeAgo = (date) => {
    const now = new Date();
    const notifDate = new Date(date);
    const diffInMinutes = Math.floor((now - notifDate) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return notifDate.toLocaleDateString();
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef} style={{ zIndex: 10000 }}>
      {/* Bell Icon */}
      <button
        ref={buttonRef}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('🔔 Notification bell clicked, isOpen:', isOpen, '-> will be:', !isOpen);
          setIsOpen(!isOpen);
        }}
        className="notification-bell-button relative p-2 text-gray-700 hover:text-blue-600 transition-colors"
        style={{ zIndex: 10001 }}
      >
        {unreadCount > 0 ? (
          <TbBellRinging
            className="w-5 h-5"
            style={{ color: '#ef4444' }}
          />
        ) : (
          <TbBell
            className="w-5 h-5"
            style={{ color: '#374151' }}
          />
        )}

        {/* Unread count badge - positioned below the bell */}
        {unreadCount > 0 && (
          <span
            className="notification-badge"
            style={{
              position: 'absolute',
              bottom: '-4px',
              left: '50%',
              transform: 'translateX(-50%)',
              backgroundColor: '#ef4444',
              color: '#ffffff',
              fontSize: '10px',
              fontWeight: '700',
              minWidth: '20px',
              width: '20px',
              height: '20px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '2px solid #ffffff',
              boxShadow: '0 2px 8px rgba(239, 68, 68, 0.6)',
              zIndex: 1000
            }}
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Dropdown Portal */}
      {isOpen && createPortal(
          <div
            ref={dropdownRef}
            className="notification-dropdown"
              style={{
                position: 'fixed',
                top: dropdownPosition.top,
                right: dropdownPosition.right,
                zIndex: 99999,
                width: '384px',
                maxHeight: '500px',
                backgroundColor: 'white',
                borderRadius: '16px',
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',
                border: '1px solid #e5e7eb',
                overflow: 'hidden'
              }}
              onClick={(e) => e.stopPropagation()}
            >
            {/* Test Header */}
            <div style={{ padding: '16px', borderBottom: '1px solid #e5e7eb', backgroundColor: '#f8fafc' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <TbBell style={{ width: '20px', height: '20px', color: '#2563eb' }} />
                <h3 style={{ fontWeight: 'bold', color: '#1f2937', margin: 0 }}>Notifications</h3>
                {unreadCount > 0 && (
                  <span style={{
                    backgroundColor: '#ef4444',
                    color: 'white',
                    fontSize: '12px',
                    padding: '4px 8px',
                    borderRadius: '9999px',
                    fontWeight: 'bold'
                  }}>
                    {unreadCount}
                  </span>
                )}
              </div>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <button
                    onClick={handleMarkAllAsRead}
                    className="text-sm text-blue-600 hover:text-blue-700 font-medium px-2 py-1 rounded hover:bg-blue-100 transition-colors"
                  >
                    <TbCheck className="w-4 h-4 inline mr-1" />
                    Mark all read
                  </button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600 p-1 rounded hover:bg-gray-100 transition-colors"
                >
                  <TbX className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Test Content */}
            <div style={{ maxHeight: '320px', overflowY: 'auto' }}>
              <div style={{ padding: '16px', textAlign: 'center', color: '#6b7280' }}>
                <div style={{ marginBottom: '8px' }}>🔔 Notification Dropdown Test</div>
                <div style={{ fontSize: '14px' }}>
                  Loading: {loading ? 'Yes' : 'No'}<br/>
                  Notifications: {notifications.length}<br/>
                  Unread: {unreadCount}
                </div>
              </div>
              {loading && notifications.length === 0 ? (
                <div style={{ padding: '16px', textAlign: 'center', color: '#6b7280' }}>
                  <div style={{
                    width: '24px',
                    height: '24px',
                    border: '2px solid #2563eb',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite',
                    margin: '0 auto 8px'
                  }}></div>
                  Loading notifications...
                </div>
              ) : notifications.length === 0 ? (
                <div style={{ padding: '32px', textAlign: 'center', color: '#6b7280' }}>
                  <TbBell style={{ width: '48px', height: '48px', margin: '0 auto 8px', opacity: 0.5 }} />
                  <p style={{ margin: 0 }}>No notifications yet</p>
                </div>
              ) : (
                notifications.map((notification) => (
                  <div
                    key={notification._id}
                    className={`notification-item group relative transition-all duration-200 ${
                      !notification.isRead
                        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div
                      className="p-4 cursor-pointer"
                      onClick={() => {
                        if (!notification.isRead) {
                          handleMarkAsRead(notification._id);
                        }
                        if (notification.actionUrl) {
                          window.location.href = notification.actionUrl;
                        }
                      }}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg flex-shrink-0 ${
                          !notification.isRead ? 'bg-blue-100' : 'bg-gray-100'
                        }`}>
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className={`font-semibold text-sm line-clamp-1 ${
                                !notification.isRead ? 'text-gray-900' : 'text-gray-700'
                              }`}>
                                {notification.title}
                              </p>
                              <p className="text-gray-600 text-xs mt-1 line-clamp-2">
                                {notification.message}
                              </p>
                              <div className="flex items-center justify-between mt-2">
                                <p className="text-gray-400 text-xs">
                                  {formatTimeAgo(notification.createdAt)}
                                </p>
                                {!notification.isRead && (
                                  <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                                    New
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Individual close button */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMarkAsRead(notification._id);
                        // Remove from local state
                        setNotifications(prev => prev.filter(n => n._id !== notification._id));
                      }}
                      className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-gray-200 text-gray-400 hover:text-gray-600"
                    >
                      <TbX className="w-3 h-3" />
                    </button>
                  </div>
                ))
              )}
              
              {/* Load More */}
              {hasMore && notifications.length > 0 && (
                <button
                  onClick={() => fetchNotifications(page + 1)}
                  disabled={loading}
                  className="w-full p-3 text-center text-blue-600 hover:bg-gray-50 text-sm font-medium disabled:opacity-50"
                >
                  {loading ? 'Loading...' : 'Load more'}
                </button>
              )}
            </div>
          </div>,
        document.body
      )}
    </div>
  );
};

export default NotificationBell;

// Add CSS animations to replace Framer Motion
const styles = `
.notification-bell-button {
  transition: transform 0.2s ease;
}

.notification-bell-button:hover {
  transform: scale(1.05);
}

.notification-bell-button:active {
  transform: scale(0.95);
}

.notification-badge {
  animation: badgeAppear 0.3s ease-out;
}

.notification-dropdown {
  animation: dropdownAppear 0.3s ease-out;
}

.notification-item {
  animation: itemSlideIn 0.3s ease-out;
}

@keyframes badgeAppear {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes dropdownAppear {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes itemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
